'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import { CalendarIcon, Upload } from 'lucide-react';
import { format } from 'date-fns';
import { pl } from 'date-fns/locale';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@workspace/ui/components/accordion';
import { Button } from '@workspace/ui/components/button';
import { Calendar } from '@workspace/ui/components/calendar';
import { Card, CardContent } from '@workspace/ui/components/card';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@workspace/ui/components/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@workspace/ui/components/select';
import { Separator } from '@workspace/ui/components/separator';
import { Textarea } from '@workspace/ui/components/textarea';
import { Badge } from '@workspace/ui/components/badge';
import { toast } from '@workspace/ui/components/sonner';
import { cn } from '@workspace/ui/lib/utils';

import { useZodForm } from '~/hooks/use-zod-form';
import { useActiveOrganization } from '~/hooks/use-active-organization';
import { INVOICE_FORM_LABELS } from '~/lib/invoice-form-constants';
import { formatPolishCurrency } from '~/lib/polish-formatters';
import { createInvoiceSchema, type CreateInvoiceSchema } from '~/schemas/financial-documents/create-invoice-schema';
import { createInvoiceAction, saveInvoiceDraftAction, getNextInvoiceNumberAction, checkInvoiceNumberExistsAction, getLastInvoiceNumberAction } from '~/actions/financial-documents/create-invoice-action';
import { DocumentStatus, DocumentType, PaymentStatus, PaymentMethod } from '@workspace/database/queries';
import { ContractorSelector } from './contractor-selector';
import { PdfPreviewButton } from './pdf-actions';
import { InvoiceLineItems } from './invoice-line-items';
import { LastInvoiceInfo } from './last-invoice-info';
import { validateInvoiceNumberSequence } from '~/lib/invoice-utils';
import type { ContractorDto } from '~/types/dtos/contractor-dto';
import type { OrganizationDetailsDto } from '~/types/dtos/organization-details-dto';

interface InvoiceFormProps {
  contractors: ContractorDto[];
  organizationDetails: OrganizationDetailsDto;
  invoice?: any; // For editing existing invoices
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function InvoiceForm({
  contractors,
  organizationDetails,
  invoice,
  onSuccess,
  onCancel
}: InvoiceFormProps): React.JSX.Element {
  const router = useRouter();
  const organization = useActiveOrganization();
  const isEditing = !!invoice;

  // Set up default values
  const today = new Date();
  const defaultDueDate = new Date(today);
  defaultDueDate.setDate(today.getDate() + 14); // 14 days from today

  // State for invoice number and validation
  const [invoiceNumber, setInvoiceNumber] = React.useState<string>('');
  const [lastInvoiceInfo, setLastInvoiceInfo] = React.useState<{
    invoiceNumber: string;
    issueDate: Date;
  } | null>(null);
  const [invoiceNumberError, setInvoiceNumberError] = React.useState<string>('');
  const [isValidatingInvoiceNumber, setIsValidatingInvoiceNumber] = React.useState(false);

  // Load last invoice info
  const loadLastInvoiceInfo = React.useCallback(async () => {
    try {
      const result = await getLastInvoiceNumberAction();
      if (result?.data?.success && result.data.lastInvoice) {
        setLastInvoiceInfo({
          invoiceNumber: result.data.lastInvoice.invoiceNumber,
          issueDate: new Date(result.data.lastInvoice.issueDate)
        });
      } else {
        setLastInvoiceInfo(null);
      }
    } catch (error) {
      console.error('Error loading last invoice info:', error);
      setLastInvoiceInfo(null);
    }
  }, []);

  // Generate sequential invoice number
  const generateInvoiceNumber = React.useCallback(async () => {
    console.log('🔍 Client: generateInvoiceNumber called');
    try {
      console.log('📞 Client: Calling getNextInvoiceNumberAction...');
      const result = await getNextInvoiceNumberAction();
      console.log('📋 Client: Server action result:', result);

      if (result?.data?.success && result.data.invoiceNumber) {
        console.log('✅ Client: Success, returning invoice number:', result.data.invoiceNumber);
        return result.data.invoiceNumber;
      } else {
        // If server action fails, show error and return placeholder
        const errorMessage = (result?.data as any)?.error || 'Unknown error';
        console.error('❌ Client: Server action failed to generate invoice number:', errorMessage);
        console.error('❌ Client: Full result object:', result);
        toast.error('Błąd podczas generowania numeru faktury. Spróbuj ponownie.');

        // Return a placeholder that indicates the issue
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const fallback = `FV/${year}/${month}/01`;
        console.log('🔄 Client: Using fallback:', fallback);
        return fallback; // Always start with 01 as fallback
      }
    } catch (error) {
      console.error('💥 Client: Error generating invoice number:', error);
      console.error('💥 Client: Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack',
        error
      });
      toast.error('Błąd podczas generowania numeru faktury. Spróbuj ponownie.');

      // Return a placeholder that indicates the issue
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const fallback = `FV/${year}/${month}/01`;
      console.log('🔄 Client: Using error fallback:', fallback);
      return fallback; // Always start with 01 as fallback
    }
  }, []);

  // Validate invoice number
  const validateInvoiceNumber = React.useCallback(async (invoiceNumber: string) => {
    if (!invoiceNumber) {
      setInvoiceNumberError('');
      return;
    }

    setIsValidatingInvoiceNumber(true);
    setInvoiceNumberError('');

    try {
      console.log('Validating invoice number:', invoiceNumber);
      console.log('Last invoice info:', lastInvoiceInfo);

      // Check format and sequence
      const sequenceValidation = validateInvoiceNumberSequence(
        invoiceNumber,
        lastInvoiceInfo?.invoiceNumber || null
      );

      console.log('Sequence validation result:', sequenceValidation);

      if (!sequenceValidation.isValid) {
        setInvoiceNumberError(sequenceValidation.error || 'Nieprawidłowy numer faktury');
        setIsValidatingInvoiceNumber(false);
        return;
      }

      // Check for duplicates
      const duplicateCheck = await checkInvoiceNumberExistsAction({
        invoiceNumber,
        excludeId: isEditing ? invoice?.id : undefined
      });

      console.log('Duplicate check result:', duplicateCheck);

      if (duplicateCheck?.data?.success && duplicateCheck.data.exists) {
        setInvoiceNumberError(`Faktura o numerze ${invoiceNumber} już istnieje. Proszę użyć innego numeru.`);
      } else if (!duplicateCheck?.data?.success) {
        console.error('Duplicate check failed:', duplicateCheck?.data?.error);
        setInvoiceNumberError('Błąd podczas sprawdzania duplikatów numeru faktury');
      }
    } catch (error) {
      console.error('Error validating invoice number:', error);
      setInvoiceNumberError('Błąd podczas sprawdzania numeru faktury');
    } finally {
      setIsValidatingInvoiceNumber(false);
    }
  }, [lastInvoiceInfo?.invoiceNumber, isEditing, invoice?.id]);

  const defaultValues: CreateInvoiceSchema = React.useMemo(() => {
    if (isEditing && invoice) {
      // Convert existing invoice data to form format
      return {
        // TODO: Map existing invoice data properly
        invoiceNumber: invoice.invoiceNumber || 'FV/YYYY/MM/01',
        issueDate: invoice.issueDate || today,
        saleDate: invoice.saleDate || today,
        dueDate: invoice.dueDate || defaultDueDate,
        contractorId: invoice.contractorId || null,
        newContractor: {
          companyName: '',
          nip: '',
          street: '',
          postalCode: '',
          city: '',
          country: '',
          email: ''
        },
        lineItems: invoice.lineItems || [
          {
            description: '',
            quantity: 1,
            unitOfMeasure: 'szt.',
            netPrice: 0,
            vatRate: 23,
            discount: 0,
            netAmount: 0,
            vatAmount: 0,
            grossAmount: 0
          }
        ],
        currency: invoice.currency || 'PLN',
        netAmount: invoice.netAmount || 0,
        vatAmount: invoice.vatAmount || 0,
        grossAmount: invoice.grossAmount || 0,
        paymentMethod: invoice.paymentMethod || PaymentMethod.TRANSFER,
        paymentAccountNumber: invoice.paymentAccountNumber || '',
        orderNumber: invoice.orderNumber || '',
        notes: invoice.notes || '',
        attachment: invoice.attachment || '',
        status: invoice.status || DocumentStatus.DRAFT,
        documentType: invoice.documentType || DocumentType.INVOICE,
        paymentStatus: invoice.paymentStatus || PaymentStatus.UNPAID,
        categoryId: invoice.categoryId || null
      };
    }

    return {
      invoiceNumber: invoiceNumber || 'FV/YYYY/MM/01', // Use generated number or placeholder
      issueDate: today,
      saleDate: today,
      dueDate: defaultDueDate,
      contractorId: null,
      newContractor: {
        companyName: '',
        nip: '',
        street: '',
        postalCode: '',
        city: '',
        country: '',
        email: ''
      },
      lineItems: [
        {
          description: '',
          quantity: 1,
          unitOfMeasure: 'szt.',
          netPrice: 0,
          vatRate: 23,
          discount: 0,
          netAmount: 0,
          vatAmount: 0,
          grossAmount: 0
        }
      ],
      currency: 'PLN',
      netAmount: 0,
      vatAmount: 0,
      grossAmount: 0,
      paymentMethod: PaymentMethod.TRANSFER,
      paymentAccountNumber: '',
      orderNumber: '',
      notes: '',
      attachment: '',
      status: DocumentStatus.DRAFT,
      documentType: DocumentType.INVOICE,
      paymentStatus: PaymentStatus.UNPAID
    };
  }, [isEditing, invoice, invoiceNumber, today, defaultDueDate]);

  const methods = useZodForm({
    schema: createInvoiceSchema,
    mode: 'onBlur',
    defaultValues
  });

  // Load invoice number and last invoice info on component mount
  React.useEffect(() => {
    const loadData = async () => {
      // Load last invoice info first
      await loadLastInvoiceInfo();

      // Then generate invoice number if not editing
      if (!isEditing && !invoiceNumber) {
        const newNumber = await generateInvoiceNumber();
        setInvoiceNumber(newNumber);
        // Update the form with the new invoice number
        methods.setValue('invoiceNumber', newNumber);
      }
    };

    loadData();
  }, [isEditing, invoiceNumber, generateInvoiceNumber, loadLastInvoiceInfo, methods]);

  // Handle form submission
  const onSubmit = methods.handleSubmit(async (formData) => {
    try {
      console.log('Form submission started with data:', formData);

      // Check for invoice number errors before submitting
      if (invoiceNumberError) {
        toast.error(invoiceNumberError);
        return;
      }

      // Validate required fields before submission
      if (!formData.contractorId && !formData.newContractor) {
        toast.error('Wybierz kontrahenta lub dodaj nowego');
        return;
      }

      if (!formData.lineItems || formData.lineItems.length === 0) {
        toast.error('Dodaj co najmniej jedną pozycję na fakturze');
        return;
      }

      const result = await createInvoiceAction(formData);

      if (result?.data?.success) {
        toast.success(INVOICE_FORM_LABELS.success.invoiceCreated);

        if (onSuccess) {
          onSuccess();
        } else {
          router.push(`/organizations/${organization?.slug}/invoices/income`);
        }
      } else {
        const errorMessage = result?.data?.error || 'Wystąpił błąd podczas tworzenia faktury';
        console.error('Server action error:', errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Wystąpił błąd podczas tworzenia faktury';
      toast.error(errorMessage);
    }
  });

  // Handle save as draft
  const handleSaveAsDraft = async () => {
    try {
      // Check for invoice number errors before saving
      if (invoiceNumberError) {
        toast.error(invoiceNumberError);
        return;
      }

      const formData = methods.getValues();
      const draftData = {
        ...formData,
        status: DocumentStatus.DRAFT
      };

      console.log('Saving draft with data:', draftData);

      const result = await saveInvoiceDraftAction(draftData);

      if (result?.data?.success) {
        toast.success(INVOICE_FORM_LABELS.success.draftSaved);
      } else {
        const errorMessage = result?.data?.error || 'Wystąpił błąd podczas zapisywania szkicu';
        console.error('Draft save server action error:', errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Draft save error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Wystąpił błąd podczas zapisywania szkicu';
      toast.error(errorMessage);
    }
  };

  // Handle preview
  const handlePreview = async () => {
    try {
      // Validate form data first
      const formData = methods.getValues();
      const validationResult = createInvoiceSchema.safeParse(formData);

      if (!validationResult.success) {
        toast.error('Proszę wypełnić wszystkie wymagane pola przed podglądem');
        return;
      }

      // Import PDF generator dynamically (client-side only)
      const { InvoicePDFGenerator } = await import('~/lib/pdf-generator');

      // Get organization logo from API
      let orgLogo: string | undefined;
      try {
        const response = await fetch('/api/organization/details');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            orgLogo = result.data.logo;
          }
        }
      } catch (error) {
        console.warn('Failed to fetch organization logo:', error);
      }

      // Use organization data that's already available on the client
      const pdfData = {
        ...validationResult.data,
        organization: {
          name: organizationDetails.name,
          nip: organizationDetails.nip || undefined,
          street: organizationDetails.street || undefined,
          city: organizationDetails.city || undefined,
          postalCode: organizationDetails.postalCode || undefined,
          country: organizationDetails.country || undefined,
          email: organizationDetails.email || undefined,
          phone: organizationDetails.phone || undefined,
          logo: orgLogo,
          legalForm: organizationDetails.legalForm || undefined
        }
      };

      // Generate and preview PDF
      const pdfGenerator = new InvoicePDFGenerator();
      pdfGenerator.previewInvoicePDF(pdfData);

    } catch (error) {
      console.error('Error generating PDF preview:', error);
      toast.error('Wystąpił błąd podczas generowania podglądu PDF');
    }
  };

  const isSubmitting = methods.formState.isSubmitting;

  return (
    <div className="min-h-screen">
      <FormProvider {...methods}>
        <form onSubmit={onSubmit} className="space-y-6 pb-8">
          <Accordion type="multiple" defaultValue={['details', 'parties', 'items', 'totals']} className="w-full">
          {/* Section 1: Invoice Details */}
          <AccordionItem value="details">
            <AccordionTrigger className="text-lg font-semibold">
              {INVOICE_FORM_LABELS.sections.invoiceDetails}
            </AccordionTrigger>
            <AccordionContent>
              <Card>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <FormField
                      control={methods.control}
                      name="invoiceNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{INVOICE_FORM_LABELS.fields.invoiceNumber}</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                {...field}
                                disabled={true} // Always disabled for Polish compliance
                                readOnly
                                className={cn(
                                  'bg-muted/50 cursor-not-allowed',
                                  invoiceNumberError && 'border-destructive focus-visible:ring-destructive'
                                )}
                              />
                              <div className="absolute right-2 top-1/2 -translate-y-1/2">
                                <Badge variant="secondary" className="text-xs">
                                  Auto
                                </Badge>
                              </div>
                            </div>
                          </FormControl>
                          <div className="space-y-2">
                            <LastInvoiceInfo
                              lastInvoiceNumber={lastInvoiceInfo?.invoiceNumber}
                              lastInvoiceDate={lastInvoiceInfo?.issueDate}
                            />
                            {invoiceNumberError && (
                              <p className="text-sm text-destructive">{invoiceNumberError}</p>
                            )}
                            {!invoiceNumberError && (
                              <FormDescription>
                                Numer faktury generowany automatycznie zgodnie z polskim prawem VAT (sekwencyjnie w ramach miesiąca)
                              </FormDescription>
                            )}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={methods.control}
                      name="issueDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>{INVOICE_FORM_LABELS.fields.issueDate}</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    'w-full pl-3 text-left font-normal',
                                    !field.value && 'text-muted-foreground'
                                  )}
                                  disabled={isSubmitting}
                                >
                                  {field.value ? (
                                    format(field.value, 'dd.MM.yyyy', { locale: pl })
                                  ) : (
                                    <span>Wybierz datę</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => {
                                  const today = new Date();
                                  today.setHours(23, 59, 59, 999); // End of today
                                  const saleDate = methods.getValues('saleDate');
                                  return date > today ||
                                         date < new Date('1900-01-01') ||
                                         (saleDate && date < saleDate);
                                }}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={methods.control}
                      name="saleDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>{INVOICE_FORM_LABELS.fields.saleDate}</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    'w-full pl-3 text-left font-normal',
                                    !field.value && 'text-muted-foreground'
                                  )}
                                  disabled={isSubmitting}
                                >
                                  {field.value ? (
                                    format(field.value, 'dd.MM.yyyy', { locale: pl })
                                  ) : (
                                    <span>Wybierz datę</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => {
                                  const issueDate = methods.getValues('issueDate');
                                  const today = new Date();
                                  today.setHours(23, 59, 59, 999); // End of today
                                  return date > (issueDate || today) || date < new Date('1900-01-01');
                                }}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            {INVOICE_FORM_LABELS.helpers.saleDate}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={methods.control}
                      name="dueDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>
                            {INVOICE_FORM_LABELS.fields.dueDate}
                            <span className="text-xs text-muted-foreground ml-1">(opcjonalne)</span>
                          </FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    'w-full pl-3 text-left font-normal',
                                    !field.value && 'text-muted-foreground'
                                  )}
                                  disabled={isSubmitting}
                                >
                                  {field.value ? (
                                    format(field.value, 'dd.MM.yyyy', { locale: pl })
                                  ) : (
                                    <span>Wybierz datę</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < new Date('1900-01-01')}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            {INVOICE_FORM_LABELS.helpers.dueDate}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>

          {/* Section 2: Parties */}
          <AccordionItem value="parties">
            <AccordionTrigger className="text-lg font-semibold">
              {INVOICE_FORM_LABELS.sections.parties}
            </AccordionTrigger>
            <AccordionContent>
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    {/* Seller Information (Read-only) */}
                    <div>
                      <h4 className="text-sm font-medium mb-3">{INVOICE_FORM_LABELS.fields.seller}</h4>
                      <div className="rounded-lg border p-4 bg-muted/50">
                        <div className="space-y-2">
                          <p className="font-medium">{organizationDetails.name}</p>
                          {organizationDetails.nip && <p>NIP: {organizationDetails.nip}</p>}
                          {organizationDetails.street && organizationDetails.city && (
                            <p>{organizationDetails.street}, {organizationDetails.postalCode} {organizationDetails.city}</p>
                          )}
                          {organizationDetails.email && <p>{organizationDetails.email}</p>}
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Buyer Information */}
                    <div>
                      <h4 className="text-sm font-medium mb-3">{INVOICE_FORM_LABELS.fields.buyer}</h4>
                      <ContractorSelector
                        form={methods as any}
                        contractors={contractors}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>

          {/* Section 3: Invoice Items */}
          <AccordionItem value="items">
            <AccordionTrigger className="text-lg font-semibold">
              {INVOICE_FORM_LABELS.sections.invoiceItems}
            </AccordionTrigger>
            <AccordionContent>
              <Card>
                <CardContent className="pt-6">
                  <InvoiceLineItems
                    form={methods as any}
                    disabled={isSubmitting}
                  />
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>

          {/* Section 4: Totals */}
          <AccordionItem value="totals">
            <AccordionTrigger className="text-lg font-semibold">
              {INVOICE_FORM_LABELS.sections.totals}
            </AccordionTrigger>
            <AccordionContent>
              <Card>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div className="space-y-4">
                      <FormField
                        control={methods.control}
                        name="currency"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{INVOICE_FORM_LABELS.fields.currency}</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={isSubmitting}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {INVOICE_FORM_LABELS.options.currencies.map((currency) => (
                                  <SelectItem key={currency.value} value={currency.value}>
                                    {currency.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              {INVOICE_FORM_LABELS.helpers.currency}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <div className="rounded-lg border p-4 bg-muted/50">
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">{INVOICE_FORM_LABELS.fields.netTotal}:</span>
                            <span className="font-mono">
                              {formatPolishCurrency(methods.watch('netAmount') || 0, methods.watch('currency'))}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">{INVOICE_FORM_LABELS.fields.vatTotal}:</span>
                            <span className="font-mono">
                              {formatPolishCurrency(methods.watch('vatAmount') || 0, methods.watch('currency'))}
                            </span>
                          </div>
                          <Separator />
                          <div className="flex justify-between items-center text-lg font-bold">
                            <span>{INVOICE_FORM_LABELS.fields.grossTotal}:</span>
                            <span className="font-mono">
                              {formatPolishCurrency(methods.watch('grossAmount') || 0, methods.watch('currency'))}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>

          {/* Section 5: Additional Information */}
          <AccordionItem value="additional">
            <AccordionTrigger className="text-lg font-semibold">
              {INVOICE_FORM_LABELS.sections.additionalInfo}
            </AccordionTrigger>
            <AccordionContent>
              <Card>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <FormField
                      control={methods.control}
                      name="paymentMethod"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{INVOICE_FORM_LABELS.fields.paymentMethod}</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={isSubmitting}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {INVOICE_FORM_LABELS.options.paymentMethods.map((method) => (
                                <SelectItem key={method.value} value={method.value}>
                                  {method.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            {INVOICE_FORM_LABELS.helpers.paymentMethod}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={methods.control}
                      name="paymentAccountNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {INVOICE_FORM_LABELS.fields.bankAccount}
                            <span className="text-xs text-muted-foreground ml-1">(opcjonalne)</span>
                          </FormLabel>
                          <FormControl>
                            <Input {...field} disabled={isSubmitting} />
                          </FormControl>
                          <FormDescription>
                            {INVOICE_FORM_LABELS.helpers.bankAccount}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={methods.control}
                      name="orderNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {INVOICE_FORM_LABELS.fields.orderNumber}
                            <span className="text-xs text-muted-foreground ml-1">(opcjonalne)</span>
                          </FormLabel>
                          <FormControl>
                            <Input {...field} disabled={isSubmitting} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={methods.control}
                      name="attachment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {INVOICE_FORM_LABELS.fields.attachment}
                            <span className="text-xs text-muted-foreground ml-1">(opcjonalne)</span>
                          </FormLabel>
                          <FormControl>
                            <div className="flex items-center space-x-2">
                              <Input
                                type="file"
                                accept=".pdf,.jpg,.jpeg,.png"
                                disabled={isSubmitting}
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) {
                                    // TODO: Handle file upload
                                    field.onChange(file.name);
                                  }
                                }}
                              />
                              <Button type="button" variant="outline" size="sm" disabled={isSubmitting}>
                                <Upload className="h-4 w-4" />
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="mt-6">
                    <FormField
                      control={methods.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {INVOICE_FORM_LABELS.fields.notes}
                            <span className="text-xs text-muted-foreground ml-1">(opcjonalne)</span>
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              rows={4}
                              disabled={isSubmitting}
                              placeholder="Dodatkowe uwagi lub informacje..."
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </AccordionContent>
          </AccordionItem>
          </Accordion>

          {/* Form Actions */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-end">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel || (() => router.back())}
                  disabled={isSubmitting}
                >
                  {INVOICE_FORM_LABELS.buttons.cancel}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePreview}
                  disabled={isSubmitting}
                >
                  {INVOICE_FORM_LABELS.buttons.preview}
                </Button>

                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleSaveAsDraft}
                  disabled={isSubmitting || !!invoiceNumberError || isValidatingInvoiceNumber}
                >
                  {INVOICE_FORM_LABELS.buttons.saveAsDraft}
                </Button>

                <Button
                  type="submit"
                  disabled={isSubmitting || !!invoiceNumberError || isValidatingInvoiceNumber}
                >
                  {isSubmitting ? 'Przetwarzanie...' : INVOICE_FORM_LABELS.buttons.issueInvoice}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </FormProvider>
    </div>
  );
}
