'use server';

import { revalidatePath } from 'next/cache';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { updateContractorSchema } from '~/schemas/contractors/update-contractor-schema';
import { routes, replaceOrgSlug } from '@workspace/routes';

export const updateContractor = authOrganizationActionClient
  .metadata({ actionName: 'updateContractor' })
  .schema(updateContractorSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      console.log('Updating contractor with input:', JSON.stringify(parsedInput, null, 2));

      if (!parsedInput.id) {
        throw new Error('Contractor ID is required for update');
      }

      // Find the contractor to update
      const existingContractor = await prisma.contractor.findUnique({
        where: {
          id: parsedInput.id,
          organizationId: ctx.organization.id
        }
      });

      if (!existingContractor) {
        throw new NotFoundError('Contractor not found');
      }

      // Prepare update data with proper typing
      const updateData = {
        shortName: parsedInput.shortName,
        fullName: parsedInput.fullName,
        nip: parsedInput.nip,
        regon: parsedInput.regon,
        country: parsedInput.country,
        postalCode: parsedInput.postalCode,
        city: parsedInput.city,
        street: parsedInput.street,
        email: parsedInput.email,
        phone: parsedInput.phone
      };

      console.log('Update data prepared:', updateData);

      // Update contractor
      const contractor = await prisma.contractor.update({
        where: {
          id: parsedInput.id
        },
        data: updateData
      });

      console.log('Contractor updated successfully:', contractor.id);

      // Revalidate contractors page
      revalidatePath(
        replaceOrgSlug(routes.dashboard.organizations.slug.Contractors, ctx.organization.slug)
      );

      // Revalidate contractor detail page
      revalidatePath(
        replaceOrgSlug(routes.dashboard.organizations.slug.Contractors_Id, ctx.organization.slug, {
          id: contractor.id
        })
      );

      return { contractor: { id: contractor.id } };
    } catch (error) {
      console.error('Error updating contractor:', error);
      throw error;
    }
  });
