'use server';

import { revalidatePath } from 'next/cache';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { createContractorSchema } from '~/schemas/contractors/create-contractor-schema';
import { routes, replaceOrgSlug } from '@workspace/routes';

export const createContractor = authOrganizationActionClient
  .metadata({ actionName: 'createContractor' })
  .schema(createContractorSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      console.log('Creating contractor with input:', JSON.stringify(parsedInput, null, 2));
      console.log('Organization ID:', ctx.organization.id);

      // Validate required fields
      if (!parsedInput.fullName) {
        throw new Error('Missing required fields for contractor creation');
      }

      // Create contractor with Prisma client
      const contractor = await prisma.contractor.create({
        data: {
          organizationId: ctx.organization.id,
          shortName: parsedInput.shortName || null,
          fullName: parsedInput.fullName,
          nip: parsedInput.nip || null,
          regon: parsedInput.regon || null,
          country: parsedInput.country || null,
          postalCode: parsedInput.postalCode || null,
          city: parsedInput.city || null,
          street: parsedInput.street || null,
          email: parsedInput.email || null,
          phone: parsedInput.phone || null
        }
      });

      console.log('Contractor created successfully:', contractor.id);

      // Revalidate contractors page
      revalidatePath(
        replaceOrgSlug(routes.dashboard.organizations.slug.Contractors, ctx.organization.slug)
      );

      return { contractor: { id: contractor.id } };
    } catch (error) {
      console.error('Error creating contractor:', error);
      throw error;
    }
  });
