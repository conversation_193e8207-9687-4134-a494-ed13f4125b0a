import { NextRequest, NextResponse } from 'next/server';
import { getAuthOrganizationContext } from '@workspace/auth/server';
import { getOrganizationDetails } from '~/data/organization/get-organization-details';
import { getOrganizationLogo } from '~/data/organization/get-organization-logo';

export async function GET(request: NextRequest) {
  try {
    // Get the authenticated organization context
    const ctx = await getAuthOrganizationContext();
    
    // Get organization details and logo
    const [details, logo] = await Promise.all([
      getOrganizationDetails(),
      getOrganizationLogo()
    ]);

    return NextResponse.json({
      success: true,
      data: {
        ...details,
        logo
      }
    });

  } catch (error) {
    console.error('Error fetching organization details:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch organization details'
      },
      { status: 500 }
    );
  }
}
